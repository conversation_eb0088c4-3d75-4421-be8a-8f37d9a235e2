{"permissions": {"allow": ["WebFetch(domain:docs.anthropic.com)", "Bash(find:*)", "Bash(cp:*)", "Bash(node:*)", "Bash(pnpm:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(open:*)", "Bash(ls:*)", "Bash(npm run dev:*)", "Bash(npm run:*)", "Bash(npm start)", "Bash(npx update-browserslist-db:*)", "Bash(npm install)", "Bash(grep:*)", "Bash(/dev/null)", "Bash(aws:*)", "Bash(cdk list:*)", "Bash(npx cdk:*)", "Bash(JSII_SILENCE_WARNING_UNTESTED_NODE_VERSION=1 npx cdk list)", "Bash(JSII_SILENCE_WARNING_UNTESTED_NODE_VERSION=1 npx cdk bootstrap)", "<PERSON><PERSON>(chmod:*)", "Bash(./deploy-skillsync-aws.sh:*)", "<PERSON><PERSON>(echo:*)", "Bash(export AWS_REGION=us-east-1)", "Bash(export AWS_ACCOUNT_ID=************)", "Bash(export PROJECT_NAME=bidbees)", "Bash(export ENVIRONMENT=production)", "Bash(export DB_PASSWORD=SecureBidBeesDB2024!)", "Bash(export DOCDB_PASSWORD=SecureBidBeesDocDB2024!)", "Bash(export REDIS_PASSWORD=SecureBidBeesRedis2024!)", "Bash(./deploy-bidbees-aws.sh:*)", "Bash(--cluster-name bidbees-cluster )", "Bash(--capacity-providers FARGATE )", "Bash(--default-capacity-provider-strategy capacityProvider=FARGATE,weight=1 )", "Bash(--region us-east-1 )", "Bash(--tags key=Project,value=bidbees key=Environment,value=production)", "Bash(--cluster-name bidbees-cluster )", "Bash(--capacity-providers FARGATE )", "Bash(--default-capacity-provider-strategy capacityProvider=FARGATE,weight=1 )", "Bash(--region us-east-1 )", "Bash(--cli-input-json file://api-gateway-task-def.json )", "Bash(--region us-east-1)", "Bash(--family \"bidbees-api-gateway\" )", "Bash(--network-mode \"awsvpc\" )", "Bash(--requires-compatibilities \"FARGATE\" )", "Bash(--cpu \"256\" )", "Bash(--memory \"512\" )", "Bash(--execution-role-arn \"arn:aws:iam::************:role/ecsTaskExecutionRole\" )", "Bash(--container-definitions '[\n        {\n            \"\"name\"\": \"\"api-gateway\"\",\n            \"\"image\"\": \"\"************.dkr.ecr.us-east-1.amazonaws.com/bidbees/api-gateway:latest\"\",\n            \"\"portMappings\"\": [\n                {\n                    \"\"containerPort\"\": 8080,\n                    \"\"protocol\"\": \"\"tcp\"\"\n                }\n            ],\n            \"\"essential\"\": true,\n            \"\"logConfiguration\"\": {\n                \"\"logDriver\"\": \"\"awslogs\"\",\n                \"\"options\"\": {\n                    \"\"awslogs-group\"\": \"\"/aws/ecs/bidbees/api-gateway\"\",\n                    \"\"awslogs-region\"\": \"\"us-east-1\"\",\n                    \"\"awslogs-stream-prefix\"\": \"\"ecs\"\"\n                }\n            },\n            \"\"environment\"\": [\n                {\n                    \"\"name\"\": \"\"NODE_ENV\"\",\n                    \"\"value\"\": \"\"production\"\"\n                },\n                {\n                    \"\"name\"\": \"\"PORT\"\",\n                    \"\"value\"\": \"\"8080\"\"\n                },\n                {\n                    \"\"name\"\": \"\"SERVICE_NAME\"\",\n                    \"\"value\"\": \"\"api-gateway\"\"\n                }\n            ]\n        }\n    ]' )", "Bash(--cluster bidbees-cluster )", "Bash(--service-name api-gateway )", "Bash(--task-definition bidbees-api-gateway:1 )", "Bash(--desired-count 1 )", "Bash(--launch-type FARGATE )", "Bash(--network-configuration \"awsvpcConfiguration={subnets=[subnet-0deb52dc470659a7a,subnet-01c4f1632e0478230],securityGroups=[sg-0027904314b493c0e],assignPublicIp=ENABLED}\" )", "Bash(--cluster bidbees-cluster )", "Bash(--services api-gateway )", "Bash(--query 'services[0].events[:5]' )", "Bash(--output table)", "Bash(--service-name api-gateway )", "Bash(--desired-status STOPPED )", "Bash(--query 'taskArns' )", "Bash(--output text)", "Bash(--cluster bidbees-cluster )", "Bash(--tasks arn:aws:ecs:us-east-1:************:task/bidbees-cluster/d37f77586e9c47aaaa0943de149cf5be )", "Bash(--query 'tasks[0].{StoppedReason:stoppedReason,LastStatus:lastStatus,StoppedAt:stoppedAt}' )", "Bash(--service-name api-gateway )", "Bash(--desired-status RUNNING )", "Bash(--query 'taskArns' )", "Bash(--cluster bidbees-cluster )", "Bash(--tasks arn:aws:ecs:us-east-1:************:task/bidbees-cluster/8650861f49fe461781b087e089f9d82d )", "Bash(--query 'tasks[0].{LastStatus:lastStatus,HealthStatus:healthStatus,CreatedAt:createdAt,Connectivity:connectivity}' )", "Bash(--query 'tasks[0].containers[0].{Name:name,LastStatus:lastStatus,HealthStatus:healthStatus}' )", "Bash(--log-group-name \"/aws/ecs/bidbees/api-gateway\" )", "Bash(--log-stream-name \"ecs/api-gateway/8650861f49fe461781b087e089f9d82d\" )", "Bash(--query 'events[].message' )", "Bash(--query 'logStreams[].logStreamName' )", "Bash(--cluster bidbees-cluster )", "Bash(--service api-gateway )", "Bash(--force-new-deployment )", "Bash(export JSII_SILENCE_WARNING_UNTESTED_NODE_VERSION=1)", "Bash(./deploy-service.sh:*)", "Bash(/Users/<USER>/Documents/GitHub/bid_bees_full_project/update-alb-targets.sh:*)", "Bash(./rapid-deploy.sh:*)", "<PERSON><PERSON>(sed:*)", "Bash(terraform:*)", "Bash(rg:*)", "Bash(export DB_PASSWORD=\"BidBees2024SecurePass!\")", "Bash(export DOCDB_PASSWORD=\"BidBees2024DocumentPass!\")", "Bash(brew:*)", "Bash(./deploy-to-aws.sh)", "<PERSON><PERSON>(claude-code --help)", "<PERSON><PERSON>(claude mcp:*)", "Bash(npm install:*)", "Bash(npm search:*)", "Bash(npx @modelcontextprotocol/server-filesystem:*)", "Bash(rm:*)", "<PERSON><PERSON>(claude:*)", "mcp__postgres-server__query", "WebFetch(domain:d22lvqihx05guo.cloudfront.net)", "<PERSON><PERSON>(python:*)", "Bash(timeout 5s npx @modelcontextprotocol/server-github 2 >& 1)", "Bash(timeout 3s npx @modelcontextprotocol/server-postgres postgresql://localhost/bidbees 2>&1)", "Bash(timeout 3s npx @modelcontextprotocol/server-memory 2>&1)", "Bash(timeout 3s npx @modelcontextprotocol/server-puppeteer 2>&1)", "<PERSON><PERSON>(timeout:*)", "Bash(LOG_LEVEL=INFO timeout 3s uvx mcp-server-qdrant 2>&1)", "Bash(uvx:*)", "mcp__puppeteer-server__puppeteer_navigate", "Bash(npx puppeteer browsers:*)", "Bash(SUPABASE_ACCESS_TOKEN=sb_secret_lXUmQpI575xHi73PxpT9Pw_JUw3kvof timeout 5s npx -y @supabase/mcp-server-supabase@latest --read-only 2>&1)", "Bash(SUPABASE_URL=\"https://uvksgkpxeyyssvdsxbts.supabase.co\" SUPABASE_SERVICE_ROLE_KEY=\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV2a3Nna3B4ZXl5c3N2ZHN4YnRzIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjAzNTg0MiwiZXhwIjoyMDYxNjExODQyfQ.i3R6lZOWC3Z60L326m20w-NrGh4Nasj9bi1qI6DipDY\" timeout 5s npx -y @supabase/mcp-server-supabase@latest 2>&1)"], "deny": []}, "enabledMcpjsonServers": ["context7", "supabase"], "disabledMcpjsonServers": ["filesystem", "github", "postgres", "memory", "web-search", "puppeteer"]}