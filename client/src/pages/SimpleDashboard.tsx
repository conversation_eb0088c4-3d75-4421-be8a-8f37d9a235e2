import React, { useEffect } from "react";
import { useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { useTheme } from '../contexts/ThemeContext';
import { ThemeSelector } from '../components/ThemeSelector';
import { api } from '@/lib/api';

// Simple working bidding dashboard
export default function BiddingDashboard() {
  const [, setLocation] = useLocation();
  const { colors } = useTheme();

  // Set the page title
  useEffect(() => {
    document.title = "BidBeez - Bidding Dashboard";
  }, []);

  // Fetch real dashboard data
  const { data: dashboardData, isLoading, error } = useQuery({
    queryKey: ['dashboard'],
    queryFn: () => api.getDashboard(),
    refetchInterval: 30000,
    retry: false,
  });

  // Fetch real tenders
  const { data: tendersData } = useQuery({
    queryKey: ['tenders'],
    queryFn: () => {
      const params = new URLSearchParams({ limit: '5' });
      return api.getTenders(params);
    },
    refetchInterval: 60000,
    retry: false,
  });

  const dashboardStyles = {
    dashboard: {
      minHeight: '100vh',
      background: colors.background,
      color: colors.textPrimary,
      margin: 0,
      padding: 0,
    },
    header: {
      background: colors.backgroundSecondary,
      backdropFilter: 'blur(10px)',
      padding: '1rem 2rem',
      borderBottom: `1px solid ${colors.cardBorder}`,
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)'
    },
    logo: {
      fontSize: '1.5rem',
      fontWeight: 'bold',
      color: '#667eea'
    },
    mainContent: {
      padding: '2rem',
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
      gap: '2rem',
      maxWidth: '1400px',
      margin: '0 auto'
    },
    card: {
      background: colors.cardBackground,
      backdropFilter: 'blur(10px)',
      borderRadius: '12px',
      padding: '2rem',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
      border: `1px solid ${colors.cardBorder}`,
      transition: 'transform 0.2s ease, box-shadow 0.2s ease',
      cursor: 'pointer'
    },
    cardTitle: {
      fontSize: '1.2rem',
      fontWeight: '600',
      marginBottom: '1rem',
      color: colors.textPrimary,
    },
    button: {
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white',
      border: 'none',
      borderRadius: '8px',
      padding: '1rem 2rem',
      fontSize: '1rem',
      fontWeight: '500',
      cursor: 'pointer',
      transition: 'transform 0.2s ease',
      width: '100%',
      marginTop: '1rem'
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900 mb-4">🐝 Loading BidBeez Dashboard...</div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="text-2xl font-bold text-red-600 mb-4">❌ Error loading dashboard</div>
          <div className="text-gray-600 mb-4">
            {error instanceof Error ? error.message : 'Unknown error occurred'}
          </div>
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div style={dashboardStyles.dashboard}>
      {/* Header */}
      <div style={dashboardStyles.header}>
        <div style={dashboardStyles.logo}>🐝 BidBeez - Bidding Dashboard</div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          <button
            style={{
              background: colors.navBackground,
              border: `1px solid ${colors.navBorder}`,
              borderRadius: '6px',
              padding: '0.5rem 1rem',
              cursor: 'pointer',
              color: colors.navText,
              fontSize: '0.9rem',
              transition: 'all 0.2s ease'
            }}
            onClick={() => setLocation('/')}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = colors.navBackgroundHover;
              e.currentTarget.style.color = colors.navTextHover;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = colors.navBackground;
              e.currentTarget.style.color = colors.navText;
            }}
          >
            🏠 Main Dashboard
          </button>
          <button
            style={{
              background: colors.navBackground,
              border: `1px solid ${colors.navBorder}`,
              borderRadius: '6px',
              padding: '0.5rem 1rem',
              cursor: 'pointer',
              color: colors.navText,
              fontSize: '0.9rem',
              transition: 'all 0.2s ease'
            }}
            onClick={() => setLocation('/tenders')}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = colors.navBackgroundHover;
              e.currentTarget.style.color = colors.navTextHover;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = colors.navBackground;
              e.currentTarget.style.color = colors.navText;
            }}
          >
            📋 Browse Tenders
          </button>
          <a
            href="http://localhost:3001"
            target="_blank"
            style={{
              background: colors.navBackground,
              border: `1px solid ${colors.navBorder}`,
              borderRadius: '6px',
              padding: '0.5rem 1rem',
              cursor: 'pointer',
              color: colors.navText,
              fontSize: '0.9rem',
              textDecoration: 'none',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = colors.navBackgroundHover;
              e.currentTarget.style.color = colors.navTextHover;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = colors.navBackground;
              e.currentTarget.style.color = colors.navText;
            }}
          >
            🏢 Enterprise TMS
          </a>
          <a
            href="http://localhost:3002"
            target="_blank"
            style={{
              background: colors.navBackground,
              border: `1px solid ${colors.navBorder}`,
              borderRadius: '6px',
              padding: '0.5rem 1rem',
              cursor: 'pointer',
              color: colors.navText,
              fontSize: '0.9rem',
              textDecoration: 'none',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = colors.navBackgroundHover;
              e.currentTarget.style.color = colors.navTextHover;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = colors.navBackground;
              e.currentTarget.style.color = colors.navText;
            }}
          >
            🚀 Advanced Frontend
          </a>
          <ThemeSelector />
        </div>
      </div>

      {/* Main Content */}
      <div style={dashboardStyles.mainContent}>
        {/* Bidding Overview Card */}
        <div style={dashboardStyles.card}>
          <div style={dashboardStyles.cardTitle}>📊 Bidding Overview</div>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
            <div style={{ textAlign: 'center', padding: '1rem', background: 'rgba(59, 130, 246, 0.1)', borderRadius: '8px' }}>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#3b82f6' }}>12</div>
              <div style={{ fontSize: '0.9rem', color: '#666' }}>Active Bids</div>
            </div>
            <div style={{ textAlign: 'center', padding: '1rem', background: 'rgba(34, 197, 94, 0.1)', borderRadius: '8px' }}>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#22c55e' }}>78%</div>
              <div style={{ fontSize: '0.9rem', color: '#666' }}>Win Rate</div>
            </div>
          </div>
        </div>

        {/* South African Compliance Card */}
        <div style={dashboardStyles.card}>
          <div style={dashboardStyles.cardTitle}>⚖️ SA Compliance Status</div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0.5rem', background: '#dcfce7', borderRadius: '6px' }}>
              <span>UIF Registration</span>
              <span style={{ color: '#166534', fontWeight: 'bold' }}>✅ Compliant</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0.5rem', background: '#fef3c7', borderRadius: '6px' }}>
              <span>COIDA Certificate</span>
              <span style={{ color: '#92400e', fontWeight: 'bold' }}>⚠️ Expires Soon</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0.5rem', background: '#dcfce7', borderRadius: '6px' }}>
              <span>CIDB Registration</span>
              <span style={{ color: '#166534', fontWeight: 'bold' }}>✅ Valid</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0.5rem', background: '#fee2e2', borderRadius: '6px' }}>
              <span>Electrical License</span>
              <span style={{ color: '#991b1b', fontWeight: 'bold' }}>❌ Expired</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0.5rem', background: '#dcfce7', borderRadius: '6px' }}>
              <span>SARS Tax Clearance</span>
              <span style={{ color: '#166534', fontWeight: 'bold' }}>✅ Current</span>
            </div>
          </div>
        </div>

        {/* Active Tenders Card */}
        <div style={dashboardStyles.card}>
          <div style={dashboardStyles.cardTitle}>🎯 Active Tenders</div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
            {tendersData?.tenders?.slice(0, 3).map((tender: any, index: number) => (
              <div
                key={tender.id || index}
                style={{
                  padding: '0.75rem',
                  background: '#f8fafc',
                  borderRadius: '6px',
                  borderLeft: '3px solid #667eea',
                  cursor: 'pointer'
                }}
                onClick={() => setLocation(`/tenders/${tender.id}`)}
              >
                <div style={{ fontWeight: '500', marginBottom: '0.25rem' }}>
                  {tender.title || `Tender ${index + 1}`}
                </div>
                <div style={{ fontSize: '0.8rem', color: '#888' }}>
                  {tender.issuer || 'Government Entity'} • Due: {tender.due_date ? new Date(tender.due_date).toLocaleDateString('en-ZA', {
                    timeZone: 'Africa/Johannesburg',
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                  }) + ' SAST' : 'TBD'}
                </div>
              </div>
            )) || (
              <div style={{ textAlign: 'center', padding: '2rem', color: '#666' }}>
                Loading tenders...
              </div>
            )}
            <button
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                padding: '0.75rem',
                fontSize: '0.9rem',
                fontWeight: '500',
                cursor: 'pointer',
                marginTop: '0.5rem'
              }}
              onClick={() => setLocation('/tenders')}
            >
              View All Tenders →
            </button>
          </div>
        </div>

        {/* Internal Tender Tracking Card */}
        <div style={dashboardStyles.card}>
          <div style={dashboardStyles.cardTitle}>📋 Internal Tender Tracking</div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            <div style={{ padding: '1rem', border: '1px solid #e5e7eb', borderRadius: '8px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.5rem' }}>
                <h4 style={{ fontWeight: '500' }}>Municipal Road Upgrade</h4>
                <span style={{ background: '#dbeafe', color: '#1e40af', padding: '0.25rem 0.5rem', borderRadius: '12px', fontSize: '0.8rem' }}>
                  Document Review
                </span>
              </div>
              <div style={{ background: '#f3f4f6', height: '8px', borderRadius: '4px', overflow: 'hidden' }}>
                <div style={{ height: '100%', background: '#3b82f6', width: '65%', borderRadius: '4px' }}></div>
              </div>
              <div style={{ fontSize: '0.8rem', color: '#666', marginTop: '0.5rem' }}>65% Complete • Due: Jan 15, 2025</div>
            </div>

            <div style={{ padding: '1rem', border: '1px solid #e5e7eb', borderRadius: '8px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.5rem' }}>
                <h4 style={{ fontWeight: '500' }}>School Building Project</h4>
                <span style={{ background: '#fef3c7', color: '#92400e', padding: '0.25rem 0.5rem', borderRadius: '12px', fontSize: '0.8rem' }}>
                  Pricing
                </span>
              </div>
              <div style={{ background: '#f3f4f6', height: '8px', borderRadius: '4px', overflow: 'hidden' }}>
                <div style={{ height: '100%', background: '#f59e0b', width: '40%', borderRadius: '4px' }}></div>
              </div>
              <div style={{ fontSize: '0.8rem', color: '#666', marginTop: '0.5rem' }}>40% Complete • Due: Jan 22, 2025</div>
            </div>
          </div>
        </div>

        {/* Meeting Schedule Card */}
        <div style={dashboardStyles.card}>
          <div style={dashboardStyles.cardTitle}>🤝 Upcoming Meetings</div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0.75rem', background: '#eff6ff', borderRadius: '6px' }}>
              <div>
                <div style={{ fontWeight: '500', fontSize: '0.9rem' }}>Site Inspection</div>
                <div style={{ fontSize: '0.8rem', color: '#666' }}>Municipal Road Project</div>
              </div>
              <div style={{ textAlign: 'right' }}>
                <div style={{ fontSize: '0.9rem', fontWeight: '500' }}>Jan 18</div>
                <div style={{ fontSize: '0.8rem', color: '#666' }}>10:00 AM</div>
              </div>
            </div>

            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0.75rem', background: '#f0fdf4', borderRadius: '6px' }}>
              <div>
                <div style={{ fontWeight: '500', fontSize: '0.9rem' }}>Pre-bid Meeting</div>
                <div style={{ fontSize: '0.8rem', color: '#666' }}>School Building Tender</div>
              </div>
              <div style={{ textAlign: 'right' }}>
                <div style={{ fontSize: '0.9rem', fontWeight: '500' }}>Jan 22</div>
                <div style={{ fontSize: '0.8rem', color: '#666' }}>2:00 PM</div>
              </div>
            </div>
          </div>
        </div>

        {/* Document Management Card */}
        <div style={dashboardStyles.card}>
          <div style={dashboardStyles.cardTitle}>📄 Document Status</div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0.5rem', background: '#dcfce7', borderRadius: '4px' }}>
              <span style={{ fontSize: '0.9rem' }}>Company Profile.pdf</span>
              <span style={{ color: '#166534', fontSize: '0.8rem', fontWeight: '500' }}>✓ Current</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0.5rem', background: '#dcfce7', borderRadius: '4px' }}>
              <span style={{ fontSize: '0.9rem' }}>CIDB Certificate.pdf</span>
              <span style={{ color: '#166534', fontSize: '0.8rem', fontWeight: '500' }}>✓ Valid</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0.5rem', background: '#fef3c7', borderRadius: '4px' }}>
              <span style={{ fontSize: '0.9rem' }}>Tax Clearance.pdf</span>
              <span style={{ color: '#92400e', fontSize: '0.8rem', fontWeight: '500' }}>⚠ Expires Soon</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0.5rem', background: '#fee2e2', borderRadius: '4px' }}>
              <span style={{ fontSize: '0.9rem' }}>Electrical License.pdf</span>
              <span style={{ color: '#991b1b', fontSize: '0.8rem', fontWeight: '500' }}>✗ Expired</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
