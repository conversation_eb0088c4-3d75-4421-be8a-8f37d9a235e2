import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/api';

interface SiteMeeting {
  id: string;
  title: string;
  date: string;
  status: string;
}

export default function SiteMeetingsCard() {
  const { data: meetings, isLoading } = useQuery<SiteMeeting[]>({
    queryKey: ['siteMeetings'],
    queryFn: async () => {
      try {
        const data = await api.getMeetings(3);
        return data.meetings?.map((meeting: any) => ({
          id: meeting.id,
          title: meeting.title,
          date: new Date(meeting.date).toLocaleDateString('en-ZA', {
            timeZone: 'Africa/Johannesburg',
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          }) + ' SAST',
          status: meeting.status
        })) || [];
      } catch (error) {
        // Return mock data if API not available
        console.warn('API not available, using mock data:', error);
        return [
          { id: '1', title: 'Site Inspection - Road Project', date: '2025-01-18T10:00:00Z', status: 'scheduled' },
          { id: '2', title: 'Pre-bid Meeting - School Building', date: '2025-01-22T14:00:00Z', status: 'scheduled' },
          { id: '3', title: 'Technical Review - Municipal Upgrade', date: '2025-01-25T09:00:00Z', status: 'rescheduled' }
        ].map(meeting => ({
          ...meeting,
          date: new Date(meeting.date).toLocaleDateString('en-ZA', {
            timeZone: 'Africa/Johannesburg',
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          }) + ' SAST'
        }));
      }
    }
  });

  const cardStyles = {
    card: {
      background: 'rgba(255, 255, 255, 0.95)',
      backdropFilter: 'blur(10px)',
      borderRadius: '12px',
      padding: '1.5rem',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      position: 'relative' as 'relative'
    },
    title: {
      fontSize: '1.1rem',
      fontWeight: '600',
      marginBottom: '1rem',
      color: '#333'
    },
    meetingList: {
      display: 'flex',
      flexDirection: 'column' as 'column',
      gap: '0.75rem'
    },
    meetingItem: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: '0.5rem 0'
    },
    meetingName: {
      fontSize: '0.9rem',
      color: '#333',
      fontWeight: '500'
    },
    meetingStatus: {
      padding: '0.25rem 0.75rem',
      borderRadius: '12px',
      fontSize: '0.8rem',
      fontWeight: '600',
      color: 'white'
    },
    loading: {
      textAlign: 'center' as 'center',
      color: '#666',
      fontStyle: 'italic'
    },
    emptyState: {
      textAlign: 'center' as 'center',
      color: '#666',
      fontStyle: 'italic',
      padding: '2rem 0'
    },
    badge: {
      background: 'linear-gradient(135deg, #34d399 0%, #10b981 100%)',
      color: 'white',
      padding: '0.125rem 0.5rem',
      borderRadius: '10px',
      fontSize: '0.7rem',
      fontWeight: '500',
      position: 'absolute' as 'absolute',
      top: '0.5rem',
      right: '0.5rem'
    }
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'scheduled':
        return 'linear-gradient(135deg, #34d399 0%, #10b981 100%)'; // Green
      case 'rescheduled':
        return 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)'; // Orange
      case 'cancelled':
        return 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)'; // Red
      default:
        return 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)'; // Gray
    }
  };

  return (
    <div style={cardStyles.card}>
      <div style={cardStyles.badge}>LIVE</div>
      <h3 style={cardStyles.title}>Site Meetings</h3>
      
      {isLoading ? (
        <div style={cardStyles.loading}>Loading site meetings...</div>
      ) : meetings && meetings.length > 0 ? (
        <div style={cardStyles.meetingList}>
          {meetings.map((meeting) => (
            <div key={meeting.id} style={cardStyles.meetingItem}>
              <span style={cardStyles.meetingName}>{meeting.title}</span>
              <span style={{
                ...cardStyles.meetingStatus,
                background: getStatusColor(meeting.status)
              }}>
                {meeting.date}
              </span>
            </div>
          ))}
        </div>
      ) : (
        <div style={cardStyles.emptyState}>
          No upcoming site meetings
        </div>
      )}
    </div>
  );
}
