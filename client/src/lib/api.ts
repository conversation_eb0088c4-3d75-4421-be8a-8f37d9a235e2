// API configuration for different environments
const API_BASE_URL = (() => {
  // In production (CloudFront), API calls should go to the same domain
  if (import.meta.env.PROD) {
    return '';
  }
  
  // In development, use the proxy configured in vite.config.ts
  // which redirects to the local backend server
  return '';
})();

// Generic API fetch wrapper
export async function apiCall(endpoint: string, options: RequestInit = {}) {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    throw new Error(`API call failed: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

// Specific API functions
export const api = {
  // Dashboard data
  getDashboard: () => apiCall('/api/dashboard'),
  
  // Tenders
  getTenders: (params?: URLSearchParams) => {
    const query = params ? `?${params.toString()}` : '';
    return apiCall(`/api/tenders${query}`);
  },
  
  // Meetings
  getMeetings: (limit = 10) => apiCall(`/api/meetings?limit=${limit}`),
  
  // Individual tender
  getTender: (id: string) => apiCall(`/api/tenders/${id}`),
};