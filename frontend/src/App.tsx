import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, CssBaseline } from '@mui/material';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Provider } from 'react-redux';

// Core imports
import { store } from '@/store';
import { useAppTheme } from '@/core/hooks/useTheme';
// import { AuthProvider } from '@/core/contexts/AuthContext';
// import { WebSocketProvider } from '@/core/contexts/WebSocketContext';
// import { NotificationProvider } from '@/core/contexts/NotificationProvider';

// Layout components
import DashboardLayout from '@/components/layout/DashboardLayout';
// import AuthLayout from '@/components/layout/AuthLayout';

// Page components
// import LoginPage from '@/pages/auth/LoginPage';
// import RegisterPage from '@/pages/auth/RegisterPage';
import DashboardPage from '@/pages/dashboard/DashboardPage';
import TendersPage from '@/pages/tenders/TendersPage';
import TenderDetailPage from '@/pages/tenders/TenderDetailPage';
import BidsPage from '@/pages/bids/BidsPage';
import BidDetailPage from '@/pages/bids/BidDetailPage';
import AnalyticsPage from '@/pages/analytics/AnalyticsPage';
import WarRoomPage from '@/pages/warroom/WarRoomPage';
import ProfilePage from '@/pages/profile/ProfilePage';
import SettingsPage from '@/pages/settings/SettingsPage';
import ConnectionTestPage from '@/pages/ConnectionTestPage';

// Protected route component - DISABLED FOR DEMO
// import ProtectedRoute from '@/components/auth/ProtectedRoute';

// Error boundary
import ErrorBoundary from '@/components/common/ErrorBoundary';

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    },
    mutations: {
      retry: 1,
    },
  },
});

const AppContent: React.FC = () => {
  const theme = useAppTheme();

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Routes>
          {/* All routes are now public - No authentication required */}
          <Route
            path="/"
            element={<DashboardLayout />}
          >
            <Route index element={<Navigate to="dashboard" replace />} />
            <Route path="dashboard" element={<DashboardPage />} />

            {/* Tenders */}
            <Route path="tenders" element={<TendersPage />} />
            <Route path="tenders/:id" element={<TenderDetailPage />} />

            {/* Bids */}
            <Route path="bids" element={<BidsPage />} />
            <Route path="bids/:id" element={<BidDetailPage />} />

            {/* Analytics */}
            <Route path="analytics" element={<AnalyticsPage />} />

            {/* War Room */}
            <Route path="warroom/:bidId" element={<WarRoomPage />} />

            {/* Profile & Settings */}
            <Route path="profile" element={<ProfilePage />} />
            <Route path="settings" element={<SettingsPage />} />

            {/* Connection Test */}
            <Route path="connection-test" element={<ConnectionTestPage />} />
          </Route>

          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Router>
    </ThemeProvider>
  );
};

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <Provider store={store}>
        <QueryClientProvider client={queryClient}>
          {/* Authentication providers disabled for demo access */}
          <AppContent />
        </QueryClientProvider>
      </Provider>
    </ErrorBoundary>
  );
};

export default App;
