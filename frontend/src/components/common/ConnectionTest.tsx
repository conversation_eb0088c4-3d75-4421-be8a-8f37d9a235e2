import React, { useState } from 'react';
import { connectionTester, ConnectionTestResult } from '@/utils/connectionTest';

interface ConnectionTestProps {
  onTestComplete?: (results: ConnectionTestResult[]) => void;
}

const ConnectionTest: React.FC<ConnectionTestProps> = ({ onTestComplete }) => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<ConnectionTestResult[]>([]);
  const [showDetails, setShowDetails] = useState(false);

  const runTests = async () => {
    setIsRunning(true);
    setResults([]);
    
    try {
      const testResults = await connectionTester.runAllTests();
      setResults(testResults);
      onTestComplete?.(testResults);
    } catch (error) {
      console.error('Error running connection tests:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: string) => {
    return status === 'success' ? '✅' : '❌';
  };

  const getStatusColor = (status: string) => {
    return status === 'success' ? 'text-green-600' : 'text-red-600';
  };

  const successful = results.filter(r => r.status === 'success').length;
  const failed = results.filter(r => r.status === 'error').length;
  const total = results.length;

  return (
    <div className="bg-white rounded-lg shadow-md p-6 max-w-2xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Connection Test</h2>
        <button
          onClick={runTests}
          disabled={isRunning}
          className={`px-4 py-2 rounded-md font-medium transition-colors ${
            isRunning
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {isRunning ? 'Testing...' : 'Run Tests'}
        </button>
      </div>

      {isRunning && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Running connection tests...</span>
        </div>
      )}

      {results.length > 0 && !isRunning && (
        <div className="space-y-4">
          {/* Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-2">Test Summary</h3>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-gray-800">{total}</div>
                <div className="text-sm text-gray-600">Total Tests</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">{successful}</div>
                <div className="text-sm text-gray-600">Successful</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-red-600">{failed}</div>
                <div className="text-sm text-gray-600">Failed</div>
              </div>
            </div>
            <div className="mt-3 text-center">
              <span className="text-lg font-semibold">
                Success Rate: {total > 0 ? ((successful / total) * 100).toFixed(1) : 0}%
              </span>
            </div>
          </div>

          {/* Results */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Test Results</h3>
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                {showDetails ? 'Hide Details' : 'Show Details'}
              </button>
            </div>
            
            {results.map((result, index) => (
              <div
                key={index}
                className={`border rounded-lg p-3 ${
                  result.status === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">{getStatusIcon(result.status)}</span>
                    <div>
                      <div className="font-medium text-gray-800">{result.service}</div>
                      <div className={`text-sm ${getStatusColor(result.status)}`}>
                        {result.message}
                      </div>
                    </div>
                  </div>
                  {result.responseTime && (
                    <div className="text-sm text-gray-500">
                      {result.responseTime}ms
                    </div>
                  )}
                </div>
                
                {showDetails && result.error && (
                  <div className="mt-2 p-2 bg-red-100 rounded text-sm text-red-700">
                    <strong>Error:</strong> {result.error}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Environment Info */}
          <div className="bg-blue-50 rounded-lg p-4 mt-6">
            <h3 className="text-lg font-semibold mb-2">Environment Information</h3>
            <div className="text-sm space-y-1">
              <div><strong>API Base URL:</strong> {import.meta.env.VITE_API_BASE_URL || 'Not set'}</div>
              <div><strong>Supabase URL:</strong> {import.meta.env.VITE_SUPABASE_URL || 'Not set'}</div>
              <div><strong>Environment:</strong> {import.meta.env.VITE_ENVIRONMENT || 'development'}</div>
              <div><strong>CloudFront Domain:</strong> https://d58ser5n68qmv.cloudfront.net</div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex space-x-3 pt-4">
            <button
              onClick={() => {
                const report = connectionTester.generateReport();
                console.log(report);
                navigator.clipboard?.writeText(report);
                alert('Test report copied to clipboard and logged to console');
              }}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              Copy Report
            </button>
            <button
              onClick={() => setResults([])}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
            >
              Clear Results
            </button>
          </div>
        </div>
      )}

      {results.length === 0 && !isRunning && (
        <div className="text-center py-8 text-gray-500">
          <p>Click "Run Tests" to verify your frontend-backend connections.</p>
          <p className="text-sm mt-2">This will test connections to Supabase and your backend services.</p>
        </div>
      )}
    </div>
  );
};

export default ConnectionTest;
