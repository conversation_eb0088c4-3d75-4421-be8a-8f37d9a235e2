import { testConnection } from '@/lib/supabase';
import { api } from '@/core/api/client';

export interface ConnectionTestResult {
  service: string;
  status: 'success' | 'error';
  message: string;
  responseTime?: number;
  error?: string;
}

export class ConnectionTester {
  private results: ConnectionTestResult[] = [];

  async testSupabaseConnection(): Promise<ConnectionTestResult> {
    const startTime = Date.now();
    
    try {
      const isConnected = await testConnection();
      const responseTime = Date.now() - startTime;
      
      const result: ConnectionTestResult = {
        service: 'Supabase',
        status: isConnected ? 'success' : 'error',
        message: isConnected 
          ? 'Successfully connected to Supabase database' 
          : 'Failed to connect to Supabase database',
        responseTime
      };
      
      this.results.push(result);
      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const result: ConnectionTestResult = {
        service: 'Supabase',
        status: 'error',
        message: 'Error testing Supabase connection',
        responseTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      
      this.results.push(result);
      return result;
    }
  }

  async testAPIGatewayConnection(): Promise<ConnectionTestResult> {
    const startTime = Date.now();
    
    try {
      const response = await api.get('/gateway/health');
      const responseTime = Date.now() - startTime;
      
      const result: ConnectionTestResult = {
        service: 'API Gateway',
        status: response.status === 200 ? 'success' : 'error',
        message: response.status === 200 
          ? 'Successfully connected to API Gateway' 
          : `API Gateway returned status ${response.status}`,
        responseTime
      };
      
      this.results.push(result);
      return result;
    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      const result: ConnectionTestResult = {
        service: 'API Gateway',
        status: 'error',
        message: 'Failed to connect to API Gateway',
        responseTime,
        error: error.response?.data?.message || error.message || 'Network error'
      };
      
      this.results.push(result);
      return result;
    }
  }

  async testTenderServiceConnection(): Promise<ConnectionTestResult> {
    const startTime = Date.now();
    
    try {
      const response = await api.get('/tenders');
      const responseTime = Date.now() - startTime;
      
      const result: ConnectionTestResult = {
        service: 'Tender Service',
        status: response.status === 200 ? 'success' : 'error',
        message: response.status === 200 
          ? 'Successfully connected to Tender Service' 
          : `Tender Service returned status ${response.status}`,
        responseTime
      };
      
      this.results.push(result);
      return result;
    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      const result: ConnectionTestResult = {
        service: 'Tender Service',
        status: 'error',
        message: 'Failed to connect to Tender Service',
        responseTime,
        error: error.response?.data?.message || error.message || 'Network error'
      };
      
      this.results.push(result);
      return result;
    }
  }

  async testMLServiceConnection(): Promise<ConnectionTestResult> {
    const startTime = Date.now();
    
    try {
      const response = await api.get('/ml/health');
      const responseTime = Date.now() - startTime;
      
      const result: ConnectionTestResult = {
        service: 'ML Service',
        status: response.status === 200 ? 'success' : 'error',
        message: response.status === 200 
          ? 'Successfully connected to ML Service' 
          : `ML Service returned status ${response.status}`,
        responseTime
      };
      
      this.results.push(result);
      return result;
    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      const result: ConnectionTestResult = {
        service: 'ML Service',
        status: 'error',
        message: 'Failed to connect to ML Service',
        responseTime,
        error: error.response?.data?.message || error.message || 'Network error'
      };
      
      this.results.push(result);
      return result;
    }
  }

  async runAllTests(): Promise<ConnectionTestResult[]> {
    console.log('🔍 Starting connection tests...');
    
    this.results = [];
    
    // Test Supabase connection
    console.log('Testing Supabase connection...');
    await this.testSupabaseConnection();
    
    // Test API Gateway connection
    console.log('Testing API Gateway connection...');
    await this.testAPIGatewayConnection();
    
    // Test Tender Service connection
    console.log('Testing Tender Service connection...');
    await this.testTenderServiceConnection();
    
    // Test ML Service connection
    console.log('Testing ML Service connection...');
    await this.testMLServiceConnection();
    
    console.log('✅ Connection tests completed');
    return this.results;
  }

  getResults(): ConnectionTestResult[] {
    return this.results;
  }

  getSuccessfulConnections(): ConnectionTestResult[] {
    return this.results.filter(result => result.status === 'success');
  }

  getFailedConnections(): ConnectionTestResult[] {
    return this.results.filter(result => result.status === 'error');
  }

  generateReport(): string {
    const successful = this.getSuccessfulConnections().length;
    const failed = this.getFailedConnections().length;
    const total = this.results.length;
    
    let report = `\n🔍 Connection Test Report\n`;
    report += `========================\n`;
    report += `Total Tests: ${total}\n`;
    report += `Successful: ${successful}\n`;
    report += `Failed: ${failed}\n`;
    report += `Success Rate: ${total > 0 ? ((successful / total) * 100).toFixed(1) : 0}%\n\n`;
    
    this.results.forEach(result => {
      const status = result.status === 'success' ? '✅' : '❌';
      report += `${status} ${result.service}: ${result.message}`;
      if (result.responseTime) {
        report += ` (${result.responseTime}ms)`;
      }
      if (result.error) {
        report += `\n   Error: ${result.error}`;
      }
      report += '\n';
    });
    
    return report;
  }
}

// Export a singleton instance
export const connectionTester = new ConnectionTester();

// Export convenience function
export const testAllConnections = () => connectionTester.runAllTests();
