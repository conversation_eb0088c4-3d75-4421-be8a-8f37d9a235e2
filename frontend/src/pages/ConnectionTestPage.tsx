import React from 'react';
import ConnectionTest from '@/components/common/ConnectionTest';
import { ConnectionTestResult } from '@/utils/connectionTest';

const ConnectionTestPage: React.FC = () => {
  const handleTestComplete = (results: ConnectionTestResult[]) => {
    console.log('Connection test results:', results);
    
    // Log detailed results for debugging
    results.forEach(result => {
      const status = result.status === 'success' ? '✅' : '❌';
      console.log(`${status} ${result.service}: ${result.message}`, {
        responseTime: result.responseTime,
        error: result.error
      });
    });
  };

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            BidBeez Connection Test
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            This page helps verify that your frontend application can successfully connect 
            to the backend services and Supabase database. Use this to troubleshoot 
            connection issues between your CloudFront deployment and backend infrastructure.
          </p>
        </div>

        <ConnectionTest onTestComplete={handleTestComplete} />

        <div className="mt-8 max-w-2xl mx-auto">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Troubleshooting Guide</h2>
            <div className="space-y-4 text-sm">
              <div>
                <h3 className="font-medium text-gray-800">If Supabase connection fails:</h3>
                <ul className="list-disc list-inside text-gray-600 mt-1 space-y-1">
                  <li>Check that VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set correctly</li>
                  <li>Verify your Supabase project is active and accessible</li>
                  <li>Ensure the database tables exist (supplier_quotes, tenders, etc.)</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-800">If API Gateway connection fails:</h3>
                <ul className="list-disc list-inside text-gray-600 mt-1 space-y-1">
                  <li>Check that VITE_API_BASE_URL points to the correct CloudFront/ALB endpoint</li>
                  <li>Verify CORS is configured to allow requests from your CloudFront domain</li>
                  <li>Ensure the API Gateway service is running and healthy</li>
                  <li>Check that CloudFront is properly routing /api/* requests to your backend</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-800">If microservice connections fail:</h3>
                <ul className="list-disc list-inside text-gray-600 mt-1 space-y-1">
                  <li>Verify the specific microservice is running and registered</li>
                  <li>Check service discovery and load balancing configuration</li>
                  <li>Ensure proper routing from API Gateway to microservices</li>
                  <li>Verify network connectivity between services</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8 text-center">
          <div className="bg-blue-50 rounded-lg p-4 max-w-2xl mx-auto">
            <h3 className="font-medium text-blue-800 mb-2">Current Configuration</h3>
            <div className="text-sm text-blue-700 space-y-1">
              <div><strong>Frontend URL:</strong> https://d58ser5n68qmv.cloudfront.net</div>
              <div><strong>API Base URL:</strong> {import.meta.env.VITE_API_BASE_URL || 'Not configured'}</div>
              <div><strong>Supabase URL:</strong> {import.meta.env.VITE_SUPABASE_URL || 'Not configured'}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConnectionTestPage;
