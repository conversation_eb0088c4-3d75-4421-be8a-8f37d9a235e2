<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez Connection Test</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .config-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .config-info h3 {
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 BidBeez Connection Test</h1>
        <p>This page tests the connection between your frontend and backend services.</p>
        
        <div class="config-info">
            <h3>Current Configuration</h3>
            <div><strong>Frontend URL:</strong> <span id="frontend-url">Loading...</span></div>
            <div><strong>API Base URL:</strong> <span id="api-url">https://d58ser5n68qmv.cloudfront.net/api</span></div>
            <div><strong>Supabase URL:</strong> <span id="supabase-url">https://uvksgkpxeyyssvdsxbts.supabase.co</span></div>
        </div>

        <button onclick="runAllTests()" id="test-btn">Run All Tests</button>
        <button onclick="clearResults()">Clear Results</button>
        
        <div id="results"></div>
    </div>

    <script>
        // Configuration
        const SUPABASE_URL = 'https://uvksgkpxeyyssvdsxbts.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV2a3Nna3B4ZXl5c3N2ZHN4YnRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYwMzU4NDIsImV4cCI6MjA2MTYxMTg0Mn0.AF1fLlULlM-_NUJYFEL092WETAXvpKKpawUsOidHQ70';
        const API_BASE_URL = 'https://d58ser5n68qmv.cloudfront.net/api';

        // Initialize Supabase client
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

        // Update frontend URL
        document.getElementById('frontend-url').textContent = window.location.origin;

        function addResult(service, status, message, responseTime, error) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${status}`;
            
            let content = `<strong>${status === 'success' ? '✅' : '❌'} ${service}:</strong> ${message}`;
            if (responseTime) {
                content += ` (${responseTime}ms)`;
            }
            if (error) {
                content += `<br><small><strong>Error:</strong> ${error}</small>`;
            }
            
            resultDiv.innerHTML = content;
            resultsDiv.appendChild(resultDiv);
        }

        function addInfo(message) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = `<strong>ℹ️ Info:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function setLoading(isLoading) {
            const btn = document.getElementById('test-btn');
            if (isLoading) {
                btn.innerHTML = '<span class="loading"></span> Running Tests...';
                btn.disabled = true;
            } else {
                btn.innerHTML = 'Run All Tests';
                btn.disabled = false;
            }
        }

        async function testSupabaseConnection() {
            const startTime = Date.now();
            
            try {
                const { data, error } = await supabase
                    .from('supplier_quotes')
                    .select('count', { count: 'exact', head: true });

                const responseTime = Date.now() - startTime;

                if (error) {
                    addResult('Supabase', 'error', 'Failed to connect to Supabase database', responseTime, error.message);
                    return false;
                } else {
                    addResult('Supabase', 'success', 'Successfully connected to Supabase database', responseTime);
                    return true;
                }
            } catch (err) {
                const responseTime = Date.now() - startTime;
                addResult('Supabase', 'error', 'Error testing Supabase connection', responseTime, err.message);
                return false;
            }
        }

        async function testAPIEndpoint(endpoint, serviceName) {
            const startTime = Date.now();
            
            try {
                const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });

                const responseTime = Date.now() - startTime;

                if (response.ok) {
                    const data = await response.json();
                    addResult(serviceName, 'success', `Successfully connected to ${serviceName}`, responseTime);
                    return true;
                } else {
                    const errorText = await response.text();
                    addResult(serviceName, 'error', `${serviceName} returned status ${response.status}`, responseTime, errorText);
                    return false;
                }
            } catch (err) {
                const responseTime = Date.now() - startTime;
                addResult(serviceName, 'error', `Failed to connect to ${serviceName}`, responseTime, err.message);
                return false;
            }
        }

        async function runAllTests() {
            setLoading(true);
            clearResults();
            
            addInfo('Starting connection tests...');
            
            let successCount = 0;
            let totalTests = 0;

            // Test Supabase connection
            addInfo('Testing Supabase connection...');
            totalTests++;
            if (await testSupabaseConnection()) {
                successCount++;
            }

            // Test API Gateway
            addInfo('Testing API Gateway connection...');
            totalTests++;
            if (await testAPIEndpoint('/gateway/health', 'API Gateway')) {
                successCount++;
            }

            // Test other endpoints
            const endpoints = [
                { path: '/tenders', name: 'Tender Service' },
                { path: '/ml/health', name: 'ML Service' },
                { path: '/auth/health', name: 'Auth Service' },
                { path: '/users/health', name: 'User Service' }
            ];

            for (const endpoint of endpoints) {
                addInfo(`Testing ${endpoint.name} connection...`);
                totalTests++;
                if (await testAPIEndpoint(endpoint.path, endpoint.name)) {
                    successCount++;
                }
            }

            // Summary
            const successRate = totalTests > 0 ? ((successCount / totalTests) * 100).toFixed(1) : 0;
            addInfo(`Tests completed: ${successCount}/${totalTests} successful (${successRate}% success rate)`);
            
            setLoading(false);
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
