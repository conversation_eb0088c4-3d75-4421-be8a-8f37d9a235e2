import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { z } from "zod";
import healthRoutes from "./routes/health.js";
import analyticsRoutes from "./routes/analytics.js";
import recruitmentRoutes from "./routes/recruitment.js";
// Conditionally import supabase only when needed
let supabase: any = null;

async function getSupabase() {
  if (!supabase) {
    try {
      const supabaseModule = await import("./lib/supabase.js");
      supabase = supabaseModule.supabase;
    } catch (error) {
      console.warn("Supabase not available:", error.message);
      return null;
    }
  }
  return supabase;
}
import { geocodeLocation, geocodeBuyerName } from "./utils/geocoding.js";

export async function registerRoutes(app: Express): Promise<Server> {
  // Register API routes
  app.use("/api/health", healthRoutes);
  app.use("/api/analytics", analyticsRoutes);
  app.use("/api/recruitment", recruitmentRoutes);

  // Simple authentication endpoints
  app.post("/api/auth/login", async (req: Request, res: Response) => {
    try {
      const { email, password } = req.body;

      if (!email || !password) {
        return res.status(400).json({
          success: false,
          message: "Email and password are required"
        });
      }

      // For demo purposes, accept any email/password combination
      // In production, this would validate against the database
      const user = {
        id: 1,
        email: email,
        name: "Demo User",
        role: "user"
      };

      // Set session
      req.session.userId = user.id;

      return res.json({
        success: true,
        message: "Login successful",
        user: user
      });
    } catch (error) {
      console.error("Login error:", error);
      return res.status(500).json({
        success: false,
        message: "Login failed"
      });
    }
  });

  app.post("/api/auth/logout", (req: Request, res: Response) => {
    req.session.destroy((err) => {
      if (err) {
        return res.status(500).json({
          success: false,
          message: "Logout failed"
        });
      }
      res.clearCookie('connect.sid');
      return res.json({
        success: true,
        message: "Logout successful"
      });
    });
  });

  app.get("/api/auth/me", (req: Request, res: Response) => {
    if (!req.session.userId) {
      return res.status(401).json({
        success: false,
        message: "Not authenticated"
      });
    }

    return res.json({
      success: true,
      user: {
        id: req.session.userId,
        email: "<EMAIL>",
        name: "Demo User",
        role: "user"
      }
    });
  });

  // Dashboard data endpoint
  app.get("/api/dashboard", async (req: Request, res: Response) => {
    try {
      const user = await storage.getCurrentUser();
      const userData = user || {
        name: "Sxulsh",
        profileComplete: 75,
        winStreak: 3
      };

      const dashboardData = {
        user: userData,
        tender: {
          title: "Construction in Eastern Cape",
          status: "70 Mid",
          issuer: "30",
          winChance: 80,
          lagngiacts: "arore",
          competitor: "Competitor #75.9 won 5 similar tenders"
        },
        quote: {
          id: "4156",
          amount: "R10,000",
          delayIncrease: "1%",
          submissionId: "709",
          submissionRisk: "high risk!",
          supplierRisk: "Risk"
        },
        mapboxToken: process.env.MAPBOX_ACCESS_TOKEN,
        mapMarkers: [
          { lng: 22.9375, lat: -28.7282, type: 'green', popupText: 'Western Cape Opportunity' },
          { lng: 24.9923, lat: -29.1007, type: 'yellow', popupText: 'Eastern Cape Project' },
          { lng: 28.2293, lat: -25.7479, type: 'red', popupText: 'Gauteng Tender - Critical' },
          { lng: 29.4627, lat: -23.8978, type: 'green', popupText: 'Limpopo, view this RFQ' },
          { lng: 18.4241, lat: -33.9249, type: 'yellow', popupText: 'Cape Town Infrastructure' },
          { lng: 31.0218, lat: -29.8587, type: 'orange', popupText: 'Durban Commercial Building' },
        ]
      };

      return res.status(200).json(dashboardData);
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      return res.status(500).json({ message: "Failed to fetch dashboard data" });
    }
  });

  // Tenders endpoint - now using real etenders data with filtering and pagination
  app.get("/api/tenders", async (req: Request, res: Response) => {
    try {
      console.log('Fetching real tenders from Supabase with filters:', req.query);

      // Pagination parameters
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 12;
      const offset = (page - 1) * limit;

      // Filter parameters
      const search = req.query.search as string;
      const status = req.query.status as string;
      const location = req.query.location as string;
      const category = req.query.category as string;
      const minValue = req.query.minValue as string;
      const maxValue = req.query.maxValue as string;
      const dateFrom = req.query.dateFrom as string;
      const dateTo = req.query.dateTo as string;
      const issuer = req.query.issuer as string;
      const sortBy = req.query.sortBy as string || 'created_at';
      const sortOrder = req.query.sortOrder as string || 'desc';

      // Build query
      const supabaseClient = await getSupabase();
      if (!supabaseClient) {
        throw new Error("Supabase not available");
      }

      let query = supabaseClient
        .from('tenders')
        .select('*', { count: 'exact' });

      // Apply filters (updated for tenders table schema)
      if (search) {
        query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%,category_code.ilike.%${search}%`);
      }

      if (category) {
        query = query.ilike('category_code', `%${category}%`);
      }

      if (location) {
        query = query.or(`issuer_name.ilike.%${location}%,province.ilike.%${location}%`);
      }

      if (issuer) {
        query = query.ilike('issuer_name', `%${issuer}%`);
      }

      if (minValue) {
        query = query.gte('tender_value', parseFloat(minValue));
      }

      if (maxValue) {
        query = query.lte('tender_value', parseFloat(maxValue));
      }

      if (dateFrom) {
        query = query.gte('publish_date', dateFrom);
      }

      if (dateTo) {
        query = query.lte('publish_date', dateTo);
      }

      // Apply sorting
      const ascending = sortOrder === 'asc';
      query = query.order(sortBy, { ascending });

      // Apply pagination
      const { data: tendersData, error, count } = await query.range(offset, offset + limit - 1);

      if (error) {
        console.error('Error fetching tenders data:', error);
        throw error;
      }

      console.log(`Fetched ${tendersData?.length || 0} tenders from main tenders table`);

      // Transform tenders data to match frontend interface
      const transformedTenders = tendersData?.map((tender: any) => {
        // Determine status from closing date
        let status = "Unknown";
        if (tender.closing_date) {
          const endDate = new Date(tender.closing_date);
          const now = new Date();
          status = endDate > now ? "Open" : "Closed";
        }

        // Format value
        const value = tender.tender_value && tender.tender_value > 0
          ? `${tender.currency || 'ZAR'} ${Number(tender.tender_value).toLocaleString()}`
          : "Not specified";

        // Get coordinates from issuer name or province
        const coords = geocodeBuyerName(tender.issuer_name) ||
                      geocodeLocation(tender.province) ||
                      { lat: -30.5595, lng: 22.9375 }; // Default to center of SA

        return {
          id: tender.id,
          title: tender.title || "Untitled Tender",
          status: status,
          issuer: tender.issuer_name || "Unknown Issuer",
          win_chance: Math.floor(Math.random() * 40) + 40, // Random 40-80% for now
          due_date: tender.closing_date || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          location: tender.province || tender.issuer_name || "South Africa",
          value: value,
          description: tender.description || "No description available",
          lng: coords.lng,
          lat: coords.lat,
          // Additional tender specific fields
          tender_number: tender.tender_number,
          category_code: tender.category_code,
          publish_date: tender.publish_date,
          document_link: tender.document_link
        };
      }) || [];

      // Fallback to mock data if no real data available
      if (transformedTenders.length === 0) {
        console.log('No real tenders found, falling back to mock data');
        const mockTenders = [
          {
            id: 1,
            title: "Highway Bridge Construction - N1 Extension",
            status: "Open",
            issuer: "South African National Roads Agency",
            win_chance: 75,
            due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            location: "Gauteng, South Africa",
            value: "R 15,000,000",
            description: "Construction of a new highway bridge on the N1 route extension project",
            lng: 28.2293,
            lat: -25.7479
          },
        {
          id: 2,
          title: "Municipal Water Treatment Plant Upgrade",
          status: "Open",
          issuer: "City of Cape Town",
          win_chance: 65,
          due_date: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(),
          location: "Western Cape, South Africa",
          value: "R 8,500,000",
          description: "Upgrade and modernization of existing water treatment facilities",
          lng: 18.4241,
          lat: -33.9249
        },
        {
          id: 3,
          title: "School Infrastructure Development",
          status: "Open",
          issuer: "Department of Basic Education",
          win_chance: 80,
          due_date: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000).toISOString(),
          location: "KwaZulu-Natal, South Africa",
          value: "R 12,000,000",
          description: "Construction of new classrooms and facilities for rural schools",
          lng: 31.0218,
          lat: -29.8587
        },
        {
          id: 4,
          title: "Solar Energy Installation Project",
          status: "Open",
          issuer: "Eskom Holdings SOC Ltd",
          win_chance: 55,
          due_date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(),
          location: "Northern Cape, South Africa",
          value: "R 25,000,000",
          description: "Installation of solar panels and energy infrastructure",
          lng: 21.2240,
          lat: -28.7380
        },
        {
          id: 5,
          title: "Port Infrastructure Maintenance",
          status: "Closing Soon",
          issuer: "Transnet National Ports Authority",
          win_chance: 45,
          due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          location: "Eastern Cape, South Africa",
          value: "R 6,800,000",
          description: "Maintenance and repair of port facilities and equipment",
          lng: 25.6022,
          lat: -33.9608
        }
      ];

        return res.status(200).json({
          tenders: mockTenders,
          total: mockTenders.length,
          mock: true
        });
      }

      // Apply status filter after transformation if specified
      let filteredTenders = transformedTenders;
      if (status) {
        filteredTenders = transformedTenders.filter(tender =>
          tender.status.toLowerCase() === status.toLowerCase()
        );
      }

      const totalPages = Math.ceil((count || 0) / limit);

      return res.status(200).json({
        tenders: filteredTenders,
        total: count || 0,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
        mock: false,
        source: 'tenders'
      });
    } catch (error) {
      console.error("Error fetching tenders:", error);

      // Fallback to mock data on error
      const fallbackTenders = [
        {
          id: 1,
          title: "Highway Bridge Construction - N1 Extension",
          status: "Open",
          issuer: "South African National Roads Agency",
          win_chance: 75,
          due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          location: "Gauteng, South Africa",
          value: "R 15,000,000",
          description: "Construction of a new highway bridge on the N1 route extension project",
          lng: 28.2293,
          lat: -25.7479
        }
      ];

      return res.status(200).json({
        tenders: fallbackTenders,
        total: fallbackTenders.length,
        mock: true,
        error: "Failed to fetch real data, showing fallback"
      });
    }
  });

  // Individual tender endpoint
  app.get("/api/tenders/:id", async (req: Request, res: Response) => {
    try {
      const tenderId = req.params.id;
      console.log(`Fetching tender details for ID: ${tenderId}`);

      const supabaseClient = await getSupabase();
      if (!supabaseClient) {
        return res.status(404).json({ error: 'Database not available' });
      }

      const { data: tenderData, error } = await supabaseClient
        .from('tenders')
        .select('*')
        .eq('id', tenderId)
        .single();

      if (error) {
        console.error('Error fetching tender:', error);
        return res.status(404).json({
          error: 'Tender not found',
          details: error.message
        });
      }

      if (!tenderData) {
        return res.status(404).json({ error: 'Tender not found' });
      }

      // Transform the data (updated for tenders table schema)
      const status = tenderData.closing_date
        ? (new Date(tenderData.closing_date) > new Date() ? "Open" : "Closed")
        : "Unknown";

      const value = tenderData.tender_value && tenderData.tender_value > 0
        ? `${tenderData.currency || 'ZAR'} ${Number(tenderData.tender_value).toLocaleString()}`
        : "Not specified";

      const coords = geocodeBuyerName(tenderData.issuer_name) ||
                    geocodeLocation(tenderData.province) ||
                    { lat: -30.5595, lng: 22.9375 };

      const transformedTender = {
        id: tenderData.id,
        title: tenderData.title || "Untitled Tender",
        status: status,
        issuer: tenderData.issuer_name || "Unknown Issuer",
        win_chance: Math.floor(Math.random() * 40) + 40,
        due_date: tenderData.closing_date || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        location: tenderData.province || tenderData.issuer_name || "South Africa",
        value: value,
        description: tenderData.description || "No description available",
        lng: coords.lng,
        lat: coords.lat,
        tender_number: tenderData.tender_number,
        category_code: tenderData.category_code,
        publish_date: tenderData.publish_date,
        document_link: tenderData.document_link,
        created_at: tenderData.created_at
      };

      // Publish tender viewed event to Kafka
      try {
        await fetch('http://localhost:3005/api/events/tender/viewed', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            tenderId: tenderId,
            userId: req.session?.userId || null,
          }),
        });
      } catch (kafkaError) {
        console.warn('Failed to publish tender viewed event:', kafkaError);
        // Don't fail the request if Kafka is down
      }

      return res.status(200).json({
        tender: transformedTender,
        source: 'tenders'
      });

    } catch (error) {
      console.error("Error fetching tender details:", error);
      return res.status(500).json({
        error: "Failed to fetch tender details",
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Meetings endpoint for site meetings
  app.get("/api/meetings", async (req: Request, res: Response) => {
    try {
      const limit = parseInt(req.query.limit as string) || 10;
      
      // Mock data for meetings (since Supabase may not be properly configured)
      const mockMeetings = [
        { 
          id: '1', 
          title: 'Site Inspection - Municipal Road Project', 
          date: '2025-01-18T10:00:00Z', 
          status: 'scheduled',
          location: 'Johannesburg CBD'
        },
        { 
          id: '2', 
          title: 'Pre-bid Meeting - School Building Tender', 
          date: '2025-01-22T14:00:00Z', 
          status: 'scheduled',
          location: 'Pretoria Education District'
        },
        { 
          id: '3', 
          title: 'Technical Review - Water Infrastructure', 
          date: '2025-01-25T09:00:00Z', 
          status: 'rescheduled',
          location: 'Cape Town Municipal Offices'
        },
        { 
          id: '4', 
          title: 'Compliance Check - CIDB Registration', 
          date: '2025-01-28T11:00:00Z', 
          status: 'scheduled',
          location: 'CIDB Offices, Midrand'
        }
      ];

      let meetings = mockMeetings;

      // Try to fetch from Supabase if available
      try {
        if (supabase) {
          const { data: supabaseMeetings, error } = await supabase
            .from('site_meetings')
            .select('id, title, date, status, location')
            .order('date', { ascending: true })
            .limit(limit);

          if (!error && supabaseMeetings && supabaseMeetings.length > 0) {
            meetings = supabaseMeetings;
          }
        }
      } catch (supabaseError) {
        console.log('Supabase not available, using mock data');
      }

      res.json({
        meetings: meetings.slice(0, limit),
        total: meetings.length,
        page: 1,
        limit,
        source: meetings === mockMeetings ? 'mock' : 'database'
      });

    } catch (error) {
      console.error("Error in meetings endpoint:", error);
      res.status(500).json({
        error: "Failed to fetch meetings",
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // AI Chat endpoint
  app.post("/api/chat", async (req: Request, res: Response) => {
    try {
      const schema = z.object({
        message: z.string().min(1)
      });

      const result = schema.safeParse(req.body);
      if (!result.success) {
        return res.status(400).json({ message: "Invalid message format" });
      }

      const { message } = result.data;
      
      // Save the user's message
      if (req.session.userId) {
        await storage.saveChatMessage({
          userId: req.session.userId,
          content: message,
          sender: "user"
        });
      }

      // Process the message and generate a response
      // This is a simple implementation - in production, this would connect to an AI service
      let reply = "I'm analyzing your request about ";
      
      if (message.toLowerCase().includes("tender")) {
        reply += "tenders. There are several new tenders available in your area. Would you like more specific information about any particular sector?";
      } else if (message.toLowerCase().includes("bee") || message.toLowerCase().includes("task")) {
        reply += "BEE tasks. You currently have 2 active tasks and 3 upcoming ones. Would you like me to help prioritize them?";
      } else if (message.toLowerCase().includes("quote") || message.toLowerCase().includes("pricing")) {
        reply += "pricing and quotes. Based on historical data, similar projects in this region have been quoted between R8,000 and R12,000. Would you like assistance with preparing a competitive quote?";
      } else {
        reply += `"${message}". I'm here to help with any bidding or tender-related questions you have.`;
      }

      // Save the AI's response
      if (req.session.userId) {
        await storage.saveChatMessage({
          userId: req.session.userId,
          content: reply,
          sender: "ai"
        });
      }

      return res.status(200).json({ reply });
    } catch (error) {
      console.error("Error processing chat message:", error);
      return res.status(500).json({ message: "Failed to process your message" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
